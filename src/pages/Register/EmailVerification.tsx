import React from 'react';
import { Card, Form, Input, Button, Typography, message } from 'antd';
import { useNavigate } from 'react-router-dom';
import { useLanguage } from '@/hooks/useLanguage';
import { useRegisterStore } from '@/store/registerStore';

const { Title } = Typography;

const EmailVerification: React.FC = () => {
  const navigate = useNavigate();
  const { t } = useLanguage();
  const { formData, updateFormData, setCurrentStep } = useRegisterStore();
  const [form] = Form.useForm();

  const onFinish = (values: { email: string }) => {
    updateFormData(values);
    setCurrentStep(2);
    message.success(t('auth.register.step1.form.emailRequired'));
    navigate('/register/alias');
  };

  // 初始化表单值
  React.useEffect(() => {
    if (formData.email) {
      form.setFieldsValue({ email: formData.email });
    }
  }, [formData.email, form]);

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50 p-4">
      <Card className="max-w-md w-full rounded-lg bg-white p-6 shadow-md">
        <div className="mb-6 text-center">
          <Title level={2}>{t('auth.register.step1.title')}</Title>
          <p className="mt-2 text-gray-600">
            {t('auth.register.step1.subtitle')}
          </p>
        </div>

        <Form
          form={form}
          layout="vertical"
          onFinish={onFinish}
          autoComplete="off"
        >
          <Form.Item
            label={t('auth.register.step1.form.email')}
            name="email"
            rules={[
              {
                required: true,
                message: t('auth.register.step1.form.emailRequired'),
              },
              {
                type: 'email',
                message: t('auth.register.step1.form.emailInvalid'),
              },
            ]}
          >
            <Input
              placeholder={t('auth.register.step1.form.emailPlaceholder')}
              size="large"
            />
          </Form.Item>

          <Form.Item className="mb-0">
            <Button
              type="primary"
              htmlType="submit"
              size="large"
              className="w-full"
            >
              {t('auth.register.step1.buttons.next')}
            </Button>
          </Form.Item>
        </Form>

        <div className="mt-4 text-center">
          <span className="text-gray-600">
            {t('auth.register.form.hasAccount')}{' '}
          </span>
          <Button
            type="link"
            onClick={() => navigate('/login')}
            className="p-0"
          >
            {t('auth.register.form.login')}
          </Button>
        </div>
      </Card>
    </div>
  );
};

export default EmailVerification;
