import React from 'react';
import { Outlet, useNavigate, useLocation } from 'react-router-dom';
import { useRegisterStore } from '@/store/registerStore';

const Register: React.FC = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const { setCurrentStep } = useRegisterStore();

  // 根据当前路径设置步骤
  React.useEffect(() => {
    const path = location.pathname;
    if (path.includes('/email')) {
      setCurrentStep(1);
    } else if (path.includes('/alias')) {
      setCurrentStep(2);
    } else if (path.includes('/personal-info')) {
      setCurrentStep(3);
    } else if (path.includes('/password')) {
      setCurrentStep(4);
    }
  }, [location.pathname]);

  // 初始化时重定向到第一步
  React.useEffect(() => {
    if (location.pathname === '/register') {
      navigate('/register/email', { replace: true });
    }
  }, [location.pathname, navigate]);

  return <Outlet />;
};

export default Register;
