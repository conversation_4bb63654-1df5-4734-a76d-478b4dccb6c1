import React from 'react';
import { Card, Form, Input, Button, Typography, message } from 'antd';
import { useNavigate } from 'react-router-dom';
import { useLanguage } from '@/hooks/useLanguage';
import { useRegisterStore } from '@/store/registerStore';

const { Title } = Typography;

const AliasSetup: React.FC = () => {
  const navigate = useNavigate();
  const { t } = useLanguage();
  const { formData, updateFormData, setCurrentStep } = useRegisterStore();
  const [form] = Form.useForm();

  const onFinish = (values: { alias: string }) => {
    updateFormData(values);
    setCurrentStep(3);
    message.success(t('auth.register.step2.form.aliasRequired'));
    navigate('/register/personal-info');
  };

  const onPrevious = () => {
    setCurrentStep(1);
    navigate('/register/email');
  };

  // 初始化表单值
  React.useEffect(() => {
    if (formData.alias) {
      form.setFieldsValue({ alias: formData.alias });
    }
  }, [formData.alias, form]);

  // 检查是否有邮箱数据，如果没有则重定向到第一步
  React.useEffect(() => {
    if (!formData.email) {
      message.warning('请先完成邮箱验证');
      navigate('/register/email');
    }
  }, [formData.email, navigate]);

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50 p-4">
      <Card className="max-w-md w-full rounded-lg bg-white p-6 shadow-md">
        <div className="mb-6 text-center">
          <Title level={2}>{t('auth.register.step2.title')}</Title>
          <p className="mt-2 text-gray-600">
            {t('auth.register.step2.subtitle')}
          </p>
        </div>

        <Form
          form={form}
          layout="vertical"
          onFinish={onFinish}
          autoComplete="off"
        >
          <Form.Item
            label={t('auth.register.step2.form.alias')}
            name="alias"
            help={t('auth.register.step2.form.aliasHelp')}
            rules={[
              {
                required: true,
                message: t('auth.register.step2.form.aliasRequired'),
              },
              {
                min: 3,
                message: '别名至少需要3个字符',
              },
              {
                pattern: /^[a-zA-Z0-9_-]+$/,
                message: '别名只能包含字母、数字、下划线和连字符',
              },
            ]}
          >
            <Input
              placeholder={t('auth.register.step2.form.aliasPlaceholder')}
              size="large"
            />
          </Form.Item>

          <div className="flex gap-3">
            <Button onClick={onPrevious} size="large" className="flex-1">
              {t('auth.register.step2.buttons.prev')}
            </Button>
            <Button
              type="primary"
              htmlType="submit"
              size="large"
              className="flex-1"
            >
              {t('auth.register.step2.buttons.next')}
            </Button>
          </div>
        </Form>

        <div className="mt-4 text-center">
          <span className="text-gray-600">
            {t('auth.register.form.hasAccount')}{' '}
          </span>
          <Button
            type="link"
            onClick={() => navigate('/login')}
            className="p-0"
          >
            {t('auth.register.form.login')}
          </Button>
        </div>
      </Card>
    </div>
  );
};

export default AliasSetup;
